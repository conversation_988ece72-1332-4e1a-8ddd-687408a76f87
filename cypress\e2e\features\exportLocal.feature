Feature: ExportLocal

  Background:
    Given The user is on the media list page

  # @e2e @exportLocal
  # Scenario: User exports a single file and all items
  #   When The user exports a file
  #   And The user exports all items

  # @e2e @exportLocal
  # Scenario: Verify the content of the Advanced Export pop-up
  #   When the user selects the file "e2e_audio.mp3"
  #   And the user clicks the "Export" toolbar button
  #   Then the "Advanced Export" pop-up is displayed
  #   And the "Fields" tab should be selected
  #   And the following export options should be visible:
  #     | Option Name     |
  #     | File Properties |
  #     | Transcription   |
  #     | Other Cognition |
  #   And the following buttons should be visible on the export pop-up:
  #     | Button Name   |
  #     | Cancel        |
  #     | Save Template |
  #     | Export        |

  # @e2e @exportLocal
  # Scenario: Verify Fields tab of Advanced Export pop-up
  #   When the user selects the file "e2e_audio.mp3"
  #   And the user clicks the "Export" toolbar button
  #   Then the "Advanced Export" pop-up is displayed
  #   And the "File ID" export option should be checked
  #   And the "File ID" export option should be disabled
  #   And the "Plain Text (TXT)" export option should be checked
  #   When the user checks the "Filename" export option
  #   Then the "Filename" export option should be checked
  #   When the user unchecks the "Plain Text (TXT)" export option
  #   Then the "Plain Text (TXT)" export option should not be checked

  @e2e @exportLocal
  Scenario: Verify user can edit name of each field
    When the user selects the file "e2e_audio.mp3"
    And the user clicks the "Export" toolbar button
    And the user clicks the edit icon for the "Plain Text (TXT)" field
    Then the "Rename fields" dialog should appear
    When the user renames the field "Plain Text (TXT)" to "My Custom TXT Name"
    And the user clicks the "OK" button in the dialog
    Then the export option should be renamed to "My Custom TXT Name"