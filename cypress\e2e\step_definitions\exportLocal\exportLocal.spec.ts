import {
  Before,
  DataTable,
  Given,
  Then,
  When,
} from '@badeball/cypress-cucumber-preprocessor';
import { mediaListPage } from '../../../pages/mediaListPage';

Before(() => {
  cy.LoginLandingPage();
});

Given('The user is on the media list page', () => {
  mediaListPage.goToMediaListPage();
});

When('The user exports a file', () => {
  mediaListPage.exportFile();
});

When('The user exports all items', () => {
  mediaListPage.exportAllItems();
});

When('the user selects the file {string}', (fileName: string) => {
  cy.contains('[data-testid^="files-table-row-"]', fileName)
    .first()
    .find('input[type="checkbox"]')
    .check();
});

When('the user clicks the {string} toolbar button', (buttonName: string) => {
  const buttonSelectors: { [key: string]: string } = {
    tag: '[data-test="files-bulk-tag-icon-button"]',
    export: '[data-test="files-bulk-export-icon-button"]',
    'run process': '[data-test="reprocess-file-icon-button"]',
    move: '[data-test="files-bulk-move-icon-button"]',
    delete: '[data-test="files-bulk-delete-icon-button"]',
    'send to redact': '[data-test="files-send-to-redact-icon-button"]',
  };

  const selector = buttonSelectors[buttonName.toLowerCase()];
  if (!selector) {
    throw new Error(`Button "${buttonName}" is not defined`);
  }

  cy.get(selector).click();
});

When('the user checks the {string} export option', (optionName: string) => {
  cy.get('[data-test="panel-bulk-export"]')
    .contains('label', optionName)
    .find('input[type="checkbox"]')
    .check({ force: true });
});

When('the user unchecks the {string} export option', (optionName: string) => {
  cy.get('[data-test="panel-bulk-export"]')
    .contains('label', optionName)
    .find('input[type="checkbox"]')
    .uncheck({ force: true });
});

When(
  'the user clicks the edit icon for the {string} field',
  (fieldName: string) => {
    cy.get('[data-test="panel-bulk-export"]')
      .find(`label:has(p:contains("${fieldName}")) + [data-testid="EditIcon"]`)
      .click();
  }
);

When(
  'the user renames the field {string} to {string}',
  (originalName: string, newName: string) => {
    cy.get('[role="dialog"]')
      .find(`div:has(label:contains("${originalName}"))`)
      .find('input[type="text"]')
      .clear();

    cy.get('[role="dialog"]')
      .find(`div:has(label:contains("${originalName}"))`)
      .find('input[type="text"]')
      .type(newName);
  }
);

When(
  'the user clicks the {string} button in the dialog',
  (buttonName: string) => {
    cy.get('[role="dialog"]').contains('button', buttonName).click();
  }
);

Then('the "Advanced Export" pop-up is displayed', () => {
  cy.get('[data-test="panel-bulk-export"]')
    .should('be.visible')
    .within(() => {
      cy.get('h1').contains('advanced export', { matchCase: false });
    });
});

Then('the {string} tab should be selected', (tabName: string) => {
  cy.get('[data-test="panel-bulk-export"]')
    .find('button[role="tab"]')
    .contains(tabName)
    .should('have.attr', 'aria-selected', 'true');
});

Then(
  'the following export options should be visible:',
  (dataTable: DataTable) => {
    const options = dataTable
      .rows()
      .map((row: string[]) => row[0])
      .filter(Boolean) as string[];

    cy.get('[data-test="panel-bulk-export"]').within(() => {
      options.forEach((optionName: string) => {
        cy.contains('p', optionName).should('be.visible');
      });
    });
  }
);

Then(
  'the following buttons should be visible on the export pop-up:',
  (dataTable: DataTable) => {
    const buttons = dataTable
      .rows()
      .map((row: string[]) => row[0])
      .filter(Boolean) as string[];

    cy.get('[data-test="panel-bulk-export"]').within(() => {
      buttons.forEach((buttonName: string) => {
        cy.contains('button', buttonName).should('be.visible');
      });
    });
  }
);

Then('the {string} export option should be checked', (optionName: string) => {
  cy.get('[data-test="panel-bulk-export"]')
    .contains('label', optionName)
    .find('input[type="checkbox"]')
    .should('be.checked');
});

Then(
  'the {string} export option should not be checked',
  (optionName: string) => {
    cy.get('[data-test="panel-bulk-export"]')
      .contains('label', optionName)
      .find('input[type="checkbox"]')
      .should('not.be.checked');
  }
);

Then('the {string} export option should be disabled', (optionName: string) => {
  cy.get('[data-test="panel-bulk-export"]')
    .contains('label', optionName)
    .find('input[type="checkbox"]')
    .should('be.disabled');
});

Then('the "Rename fields" dialog should appear', () => {
  cy.contains('[role="dialog"]', 'Rename fields').should('be.visible');
});

Then('the export option should be renamed to {string}', (newName: string) => {
  cy.get('[data-test="panel-bulk-export"]')
    .contains('p', newName)
    .should('be.visible');
});
